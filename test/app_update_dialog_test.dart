import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform;

void main() {
  group('App Update Store URL Tests', () {
    test('should return correct URLs for different platforms', () {
      // Test the URL logic that would be used in the dialog
      String getStoreUrl() {
        if (kIsWeb) {
          return 'https://mastercook.ai/';
        }
        
        if (Platform.isAndroid) {
          return 'https://play.google.com/store/apps/details?id=com.app.mastercookai&hl=en_IN';
        } else if (Platform.isIOS) {
          return 'https://apps.apple.com/ae/app/mastercookai/id6749132039?platform=iphone';
        } else if (Platform.isMacOS) {
          return 'https://apps.apple.com/ae/app/mastercookai/id6749132039';
        } else if (Platform.isWindows) {
          return 'https://mastercook.ai/';
        } else {
          // Linux or other platforms
          return 'https://mastercook.ai/';
        }
      }

      // Test that the function returns a valid URL
      String url = getStoreUrl();
      expect(url, isNotEmpty);
      expect(url, startsWith('https://'));
      
      // Test specific platform URLs (this will test the current platform)
      if (Platform.isMacOS) {
        expect(url, equals('https://apps.apple.com/ae/app/mastercookai/id6749132039'));
      } else if (Platform.isWindows) {
        expect(url, equals('https://mastercook.ai/'));
      }
      
      print('Current platform URL: $url');
    });
  });
}
